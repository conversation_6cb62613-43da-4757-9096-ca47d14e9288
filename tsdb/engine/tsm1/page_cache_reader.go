// Copyright 2023 Huawei Cloud Computing Technologies Co., Ltd.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package tsm1

import (
	"encoding/binary"
	"fmt"

	"github.com/influxdata/influxdb/tsdb/readcache"
)

// TSMPageCacheReader provides page-based caching for TSM file reads
type TSMPageCacheReader struct {
	accessor      *mmapAccessor
	init          bool
	startOffset   int64
	endOffset     int64
	maxPageId     int64
	maxPageOffset int64
	read          func(offset int64, size uint32, buf *[]byte, ioPriority int) ([]byte, *readcache.CachePage, error)
}

// NewTSMPageCacheReader creates a new page cache reader for TSM files
func NewTSMPageCacheReader(accessor *mmapAccessor) *TSMPageCacheReader {
	pcr := &TSMPageCacheReader{
		accessor: accessor,
	}
	if readcache.IsPageSizeVariable {
		pcr.read = pcr.ReadVariablePageSize
	} else {
		pcr.read = pcr.ReadFixPageSize
	}
	return pcr
}

// Init initializes the page cache reader with TSM file boundaries
func (pcr *TSMPageCacheReader) Init() {
	// TSM file structure: Header(5) + Blocks + Index + Footer(8)
	pcr.startOffset = 5 // After header

	// Get index start position from footer
	// Note: This assumes the accessor's mutex is already held by the caller
	footerPos := len(pcr.accessor.b) - 8
	indexStart := int64(binary.BigEndian.Uint64(pcr.accessor.b[footerPos : footerPos+8]))

	pcr.endOffset = indexStart // Before index
	pcr.maxPageId, pcr.maxPageOffset = pcr.GetMaxPageIdAndOffset()
}

// GetCachePageIdsAndOffsets gets all cache pageIds containing bytes from start to start + size
func (pcr *TSMPageCacheReader) GetCachePageIdsAndOffsets(start int64, size uint32) ([]int64, []int64, error) {
	end := (start + int64(size))
	if start < pcr.startOffset || start >= pcr.endOffset || end < pcr.startOffset || end > pcr.endOffset {
		return nil, nil, fmt.Errorf("invalid read offset of GetCachePageIdsAndOffsets() start:%v end:%v startOffset:%v endOffset:%v", start, end, pcr.startOffset, pcr.endOffset)
	}

	pageIds := make([]int64, 0, int64(size)/readcache.PageSize+1)
	pageOffsets := make([]int64, 0, int64(size)/readcache.PageSize+1)
	startPageId := ((start - pcr.startOffset) / readcache.PageSize) + 1
	startPageOffset := (startPageId-1)*readcache.PageSize + pcr.startOffset

	for ; startPageOffset < end; startPageOffset += readcache.PageSize {
		pageIds = append(pageIds, startPageId)
		pageOffsets = append(pageOffsets, startPageOffset)
		startPageId++
	}
	return pageIds, pageOffsets, nil
}

// GetMaxPageIdAndOffset calculates the maximum page ID and offset
func (pcr *TSMPageCacheReader) GetMaxPageIdAndOffset() (int64, int64) {
	over := (pcr.endOffset - pcr.startOffset) % readcache.PageSize
	if over > 0 {
		over = 1
	}
	endPageId := (pcr.endOffset-pcr.startOffset)/readcache.PageSize + over
	endPageOffset := (endPageId-1)*readcache.PageSize + pcr.startOffset
	return endPageId, endPageOffset
}

// ReadSinglePage reads a single page from cache or file
func (pcr *TSMPageCacheReader) ReadSinglePage(cacheKey string, pageOffset int64, pageSize int64, buf *[]byte) (*readcache.CachePage, []byte, error) {
	cacheIns := readcache.GetReadDataCacheIns()
	var pageCache *readcache.CachePage
	var ok bool
	if value, isGet := cacheIns.GetPageCache(cacheKey); isGet {
		pageCache, ok = value.(*readcache.CachePage)
		if !ok {
			return nil, nil, fmt.Errorf("cacheValue is not a page")
		}
		return pageCache, pageCache.Value, nil
	} else {
		pageCache := readcache.CachePagePool.Get()
		pageCache.Ref()

		// Read from TSM file using mmapAccessor
		pcr.accessor.mu.RLock()
		if int64(len(pcr.accessor.b)) < pageOffset+pageSize {
			pcr.accessor.mu.RUnlock()
			pageCache.Unref(readcache.CachePagePool)
			return nil, nil, fmt.Errorf("read beyond file boundary")
		}

		// Copy data from mmap
		pageCache.Value = make([]byte, pageSize)
		copy(pageCache.Value, pcr.accessor.b[pageOffset:pageOffset+pageSize])
		pcr.accessor.mu.RUnlock()

		pageCache.Size = pageSize
		cacheIns.AddPageCache(cacheKey, pageCache, pageSize, readcache.CachePagePool)
		return pageCache, pageCache.Value, nil
	}
}

// Read reads data using the configured read function with specified priority (matching openGemini interface)
func (pcr *TSMPageCacheReader) Read(offset int64, size uint32, buf *[]byte, ioPriority int) ([]byte, *readcache.CachePage, error) {
	return pcr.read(offset, size, buf, ioPriority)
}

// ReadWithDefaultPriority reads data using the configured read function with default priority
func (pcr *TSMPageCacheReader) ReadWithDefaultPriority(offset int64, size uint32, buf *[]byte) ([]byte, *readcache.CachePage, error) {
	return pcr.Read(offset, size, buf, IO_PRIORITY_NORMAL)
}

// ReadVariablePageSize reads variable-sized pages using Get/AddPage
func (pcr *TSMPageCacheReader) ReadVariablePageSize(offset int64, size uint32, buf *[]byte, ioPriority int) ([]byte, *readcache.CachePage, error) {
	cacheIns := readcache.GetReadDataCacheIns()
	cacheKey := cacheIns.CreateCacheKey(pcr.accessor.f.Name(), offset)
	var b []byte
	var page *readcache.CachePage
	var ok bool
	if value, isGet := cacheIns.Get(cacheKey); isGet {
		page, ok = value.(*readcache.CachePage)
		if !ok {
			return nil, nil, fmt.Errorf("cacheValue is not a page")
		}
		if page.Size >= int64(size) {
			b = page.Value[:size]
			return b, nil, nil
		}
	}

	// Read directly from mmap
	pcr.accessor.mu.RLock()
	if int64(len(pcr.accessor.b)) < offset+int64(size) {
		pcr.accessor.mu.RUnlock()
		return nil, nil, fmt.Errorf("read beyond file boundary")
	}
	b = make([]byte, size)
	copy(b, pcr.accessor.b[offset:offset+int64(size)])
	pcr.accessor.mu.RUnlock()

	cacheIns.AddPage(cacheKey, b, int64(size), readcache.CachePagePool)
	return b, nil, nil
}

// ReadFixPageSize reads fixed-size pages
func (pcr *TSMPageCacheReader) ReadFixPageSize(offset int64, size uint32, buf *[]byte, ioPriority int) ([]byte, *readcache.CachePage, error) {
	if !pcr.init {
		pcr.Init()
		pcr.init = true
	}
	var err error
	var pageBuf []byte
	var cachePage *readcache.CachePage
	cacheIns := readcache.GetReadDataCacheIns()
	pageIds, pageOffsets, err := pcr.GetCachePageIdsAndOffsets(offset, size)
	if err != nil || len(pageIds) == 0 {
		return nil, nil, err
	}
	cacheKeys := cacheIns.CreateCacheKeys(pcr.accessor.f.Name(), pageIds)
	tempEnd := readcache.PageSize
	if int64(size) < readcache.PageSize && len(pageIds) == 1 {
		tempEnd = int64(size) + (offset - (pageOffsets)[0])
	}
	tempPageSize := readcache.PageSize
	if (pageIds)[0] == pcr.maxPageId {
		tempPageSize = pcr.endOffset - pcr.maxPageOffset
	}
	// 1. fast: read one page
	if len(cacheKeys) == 1 {
		cachePage, pageBuf, err = pcr.ReadSinglePage(cacheKeys[0], pageOffsets[0], tempPageSize, buf)
		if err != nil {
			return nil, nil, err
		}
		newb := pageBuf[(offset - pageOffsets[0]):tempEnd]
		return newb, cachePage, nil
	}

	*buf = (*buf)[:0]
	b := buf
	// 2. read multi pages
	// 2.1. first page
	cachePage, pageBuf, err = pcr.ReadSinglePage(cacheKeys[0], pageOffsets[0], tempPageSize, buf)
	if err != nil {
		return nil, nil, err
	}
	*b = append(*b, pageBuf[(offset-pageOffsets[0]):tempEnd]...)
	if cachePage != nil {
		cachePage.Unref(readcache.CachePagePool)
	}

	// 2.2. middle pages
	for i := 1; i < len(cacheKeys)-1; i++ {
		cachePage, pageBuf, err = pcr.ReadSinglePage(cacheKeys[i], pageOffsets[i], readcache.PageSize, buf)
		if err != nil {
			return nil, nil, err
		}
		*b = append(*b, pageBuf...)
		if cachePage != nil {
			cachePage.Unref(readcache.CachePagePool)
		}
	}
	// 2.3. last page
	if pageIds[len(pageIds)-1] == pcr.maxPageId {
		tempPageSize = pcr.endOffset - pcr.maxPageOffset
	}
	cachePage, pageBuf, err = pcr.ReadSinglePage(cacheKeys[len(cacheKeys)-1], pageOffsets[len(pageOffsets)-1], tempPageSize, buf)
	if err != nil {
		return nil, nil, err
	}
	*b = append(*b, pageBuf[:int64(size)-int64(len(*b))]...)
	if cachePage != nil {
		cachePage.Unref(readcache.CachePagePool)
	}
	return *b, nil, nil
}

// UnrefCachePage releases a data cache page reference
func (pcr *TSMPageCacheReader) UnrefCachePage(cachePage *readcache.CachePage) {
	if cachePage != nil {
		cachePage.Unref(readcache.CachePagePool)
	}
}

// UnrefMetaCachePage releases a metadata cache page reference
func (pcr *TSMPageCacheReader) UnrefMetaCachePage(cachePage *readcache.CachePage) {
	if cachePage != nil {
		cachePage.Unref(readcache.MetaCachePool)
	}
}

// ReadDataBlock reads a data block with caching support
func (pcr *TSMPageCacheReader) ReadDataBlock(entry *IndexEntry, values []Value) ([]Value, *readcache.CachePage, error) {
	return pcr.ReadDataBlockWithPriority(entry, values, IO_PRIORITY_ULTRA_HIGH)
}

// ReadDataBlockWithPriority reads a data block with specified priority
func (pcr *TSMPageCacheReader) ReadDataBlockWithPriority(entry *IndexEntry, values []Value, ioPriority int) ([]Value, *readcache.CachePage, error) {
	// Use the page cache reader for data block reads
	data, cachePage, err := pcr.Read(entry.Offset, entry.Size, nil, ioPriority)
	if err != nil {
		return nil, nil, err
	}

	// Decode the block data
	values, err = DecodeBlock(data, values)
	if err != nil {
		pcr.UnrefCachePage(cachePage)
		return nil, nil, err
	}

	return values, cachePage, nil
}

// ReadMetaBytes reads metadata bytes with caching support
func (pcr *TSMPageCacheReader) ReadMetaBytes(entry *IndexEntry, b []byte) (uint32, []byte, *readcache.CachePage, error) {
	return pcr.ReadMetaBytesWithPriority(entry, b, IO_PRIORITY_ULTRA_HIGH)
}

// ReadMetaBytesWithPriority reads metadata bytes with specified priority
func (pcr *TSMPageCacheReader) ReadMetaBytesWithPriority(entry *IndexEntry, b []byte, ioPriority int) (uint32, []byte, *readcache.CachePage, error) {
	data, cachePage, err := pcr.Read(entry.Offset, entry.Size, &b, ioPriority)
	if err != nil {
		return 0, nil, nil, err
	}

	return entry.Size, data, cachePage, nil
}

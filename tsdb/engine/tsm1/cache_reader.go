// Copyright 2022 Huawei Cloud Computing Technologies Co., Ltd.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package tsm1

// This file contains simplified cache-related functionality extracted from openGemini
// Most openGemini-specific functionality has been removed to focus on readcache integration

import (
	"errors"

	"github.com/influxdata/influxdb/tsdb/readcache"
)

var (
	// ErrReadOutOfBounds is returned when attempting to read beyond file boundaries
	ErrReadOutOfBounds = errors.New("read beyond file boundaries")
)

// CacheEnabledReader provides cache-enabled reading functionality for TSM files
type CacheEnabledReader struct {
	accessor        *mmapAccessor
	pageCacheReader *TSMPageCacheReader
}

// NewCacheEnabledReader creates a new cache-enabled reader
func NewCacheEnabledReader(accessor *mmapAccessor) *CacheEnabledReader {
	reader := &CacheEnabledReader{
		accessor:        accessor,
		pageCacheReader: NewTSMPageCacheReader(accessor),
	}
	reader.pageCacheReader.Init()
	return reader
}

// ReadDataBlock reads a data block with caching support
func (r *CacheEnabledReader) ReadDataBlock(entry *IndexEntry, values []Value) ([]Value, *readcache.CachePage, error) {
	return r.ReadDataBlockWithPriority(entry, values, IO_PRIORITY_ULTRA_HIGH)
}

// ReadDataBlockWithPriority reads a data block with specified priority
func (r *CacheEnabledReader) ReadDataBlockWithPriority(entry *IndexEntry, values []Value, ioPriority int) ([]Value, *readcache.CachePage, error) {
	return r.pageCacheReader.ReadDataBlockWithPriority(entry, values, ioPriority)
}

// UnrefCachePage releases a cache page reference
func (r *CacheEnabledReader) UnrefCachePage(cachePage *readcache.CachePage) {
	r.pageCacheReader.UnrefCachePage(cachePage)
}

// CacheEnable returns whether caching is enabled
func (r *CacheEnabledReader) CacheEnable() bool {
	return readcache.GetReadDataCacheIns() != nil
}

// ReadWithCache reads data using page cache if available
func (r *CacheEnabledReader) ReadWithCache(offset int64, size uint32, buf *[]byte) ([]byte, *readcache.CachePage, error) {
	return r.ReadWithCacheAndPriority(offset, size, buf, IO_PRIORITY_NORMAL)
}

// ReadWithCacheAndPriority reads data using page cache with specified priority
func (r *CacheEnabledReader) ReadWithCacheAndPriority(offset int64, size uint32, buf *[]byte, ioPriority int) ([]byte, *readcache.CachePage, error) {
	if r.CacheEnable() && r.accessor.cacheEnable(ioPriority) {
		return r.pageCacheReader.Read(offset, size, buf, ioPriority)
	}

	// Fallback to direct read from mmap
	r.accessor.mu.RLock()
	defer r.accessor.mu.RUnlock()

	if int64(len(r.accessor.b)) < offset+int64(size) {
		return nil, nil, ErrReadOutOfBounds
	}

	data := make([]byte, size)
	copy(data, r.accessor.b[offset:offset+int64(size)])
	return data, nil, nil
}

// ReadMetaBytes reads metadata bytes with caching support
func (r *CacheEnabledReader) ReadMetaBytes(entry *IndexEntry, b []byte) (uint32, []byte, *readcache.CachePage, error) {
	return r.ReadMetaBytesWithPriority(entry, b, IO_PRIORITY_ULTRA_HIGH)
}

// ReadMetaBytesWithPriority reads metadata bytes with specified priority
func (r *CacheEnabledReader) ReadMetaBytesWithPriority(entry *IndexEntry, b []byte, ioPriority int) (uint32, []byte, *readcache.CachePage, error) {
	return r.pageCacheReader.ReadMetaBytesWithPriority(entry, b, ioPriority)
}
